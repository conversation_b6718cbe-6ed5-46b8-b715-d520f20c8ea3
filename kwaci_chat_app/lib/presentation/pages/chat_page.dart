import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';

class ChatPage extends ConsumerWidget {
  const ChatPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chats'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined),
            onPressed: () {
              // Start new chat functionality
              _showNewChatDialog(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: TextField(
              decoration: InputDecoration(
                hintText: AppConstants.searchPlaceholder,
                prefixIcon: const Icon(Icons.search),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  borderSide: BorderSide.none,
                ),
              ),
              onChanged: (value) {
                // Implement search functionality
              },
            ),
          ),
          
          // Chat List
          Expanded(
            child: _buildChatList(context),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showNewChatDialog(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildChatList(BuildContext context) {
    // Placeholder chat data - this will be replaced with real data later
    final placeholderChats = [
      {
        'id': '1',
        'title': 'AI Research Discussion',
        'lastMessage': 'What are the latest developments in transformer models?',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
        'avatar': 'assets/images/ai_avatar.png',
      },
      {
        'id': '2',
        'title': 'Document Analysis',
        'lastMessage': 'Can you summarize the key points from the uploaded paper?',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'avatar': 'assets/images/doc_avatar.png',
      },
      {
        'id': '3',
        'title': 'Technical Questions',
        'lastMessage': 'How does the RAG system work with citations?',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'avatar': 'assets/images/tech_avatar.png',
      },
    ];

    if (placeholderChats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              AppConstants.noChatHistoryMessage,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton.icon(
              onPressed: () {
                _showNewChatDialog(context);
              },
              icon: const Icon(Icons.add),
              label: const Text('Start New Chat'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: placeholderChats.length,
      itemBuilder: (context, index) {
        final chat = placeholderChats[index];
        return _ChatListItem(
          title: chat['title'] as String,
          lastMessage: chat['lastMessage'] as String,
          timestamp: chat['timestamp'] as DateTime,
          onTap: () {
            // Navigate to chat detail page
            _navigateToChatDetail(context, chat['id'] as String);
          },
        );
      },
    );
  }

  void _showNewChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('New Chat'),
        content: const Text('Start a new conversation with the AI assistant.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to new chat
              _navigateToChatDetail(context, 'new');
            },
            child: const Text('Start Chat'),
          ),
        ],
      ),
    );
  }

  void _navigateToChatDetail(BuildContext context, String chatId) {
    // This will be implemented when we create the chat detail page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening chat: $chatId'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

class _ChatListItem extends StatelessWidget {
  final String title;
  final String lastMessage;
  final DateTime timestamp;
  final VoidCallback onTap;

  const _ChatListItem({
    required this.title,
    required this.lastMessage,
    required this.timestamp,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        child: Icon(
          Icons.chat,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        lastMessage,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Text(
        _formatTimestamp(timestamp),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
        ),
      ),
      onTap: onTap,
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${(difference.inDays / 7).floor()}w';
    }
  }
}
